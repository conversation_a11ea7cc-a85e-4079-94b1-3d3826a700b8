#!/usr/bin/env python3
"""
FIXED Infinite Craft Automation Script
======================================

This version correctly implements the drag-and-drop mechanism:
- Elements must be dragged TO the canvas area
- The canvas is where combinations happen
- Elements are not draggable by default, so we use JavaScript

Key Discovery: There's a canvas element where items need to be dropped!
"""

import time
import json
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import NoSuchElementException, TimeoutException, WebDriverException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configuration
GAME_URL = "https://neal.fun/infinite-craft/"
GAME_STATE_FILE = "game_state.json"

def setup_driver():
    """Setup Chrome WebDriver optimized for Infinite Craft."""
    try:
        service = ChromeService(ChromeDriverManager().install())
        options = webdriver.ChromeOptions()
        
        # Anti-detection options
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.maximize_window()
        driver.implicitly_wait(3)
        
        print("✅ WebDriver setup successful")
        return driver
        
    except Exception as e:
        print(f"❌ WebDriver setup failed: {e}")
        return None

def get_elements_and_canvas(driver):
    """Get both the draggable elements and the canvas drop area."""
    try:
        # Wait for elements to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".item"))
        )
        
        # Get all .item elements
        elements = driver.find_elements(By.CSS_SELECTOR, ".item")
        valid_elements = [elem for elem in elements if elem.is_displayed() and elem.text.strip()]
        
        # Get the canvas (drop area)
        canvas = driver.find_element(By.CSS_SELECTOR, "canvas")
        
        print(f"Found {len(valid_elements)} elements and canvas at {canvas.location}")
        for i, elem in enumerate(valid_elements):
            print(f"  [{i}] {elem.text}")
            
        return valid_elements, canvas
        
    except Exception as e:
        print(f"❌ Error getting elements: {e}")
        return [], None

def drag_elements_to_canvas(driver, elem1, elem2, canvas):
    """
    Drag two elements to the canvas to combine them.
    
    This is the correct way Infinite Craft works:
    1. Drag first element to canvas
    2. Drag second element on top of first element on canvas
    """
    try:
        print(f"\n🎯 Combining: {elem1.text} + {elem2.text}")
        
        # Get canvas center for dropping
        canvas_center_x = canvas.location['x'] + canvas.size['width'] // 2
        canvas_center_y = canvas.location['y'] + canvas.size['height'] // 2
        
        print(f"  Canvas center: ({canvas_center_x}, {canvas_center_y})")
        
        # Get initial state
        initial_elements = driver.find_elements(By.CSS_SELECTOR, ".item")
        initial_texts = [e.text for e in initial_elements if e.text.strip()]
        
        # Method 1: Use ActionChains to drag to canvas
        actions = ActionChains(driver)
        
        print("  📦 Dragging first element to canvas...")
        # Drag first element to canvas center
        actions.drag_and_drop_by_offset(elem1, 
                                       canvas_center_x - elem1.location['x'],
                                       canvas_center_y - elem1.location['y']).perform()
        time.sleep(1)
        
        print("  📦 Dragging second element to canvas...")
        # Drag second element to same area (slightly offset)
        actions.drag_and_drop_by_offset(elem2,
                                       canvas_center_x - elem2.location['x'] + 20,
                                       canvas_center_y - elem2.location['y'] + 20).perform()
        time.sleep(2)
        
        # Check for new elements
        new_elements = driver.find_elements(By.CSS_SELECTOR, ".item")
        new_texts = [e.text for e in new_elements if e.text.strip()]
        
        # Find new items
        new_items = [text for text in new_texts if text not in initial_texts]
        
        if new_items:
            for item in new_items:
                print(f"  🎉 NEW ITEM: {item}")
            return new_items[0]
        else:
            print("  ⚠️  No new items detected with ActionChains")
            
            # Method 2: JavaScript-based drag to canvas
            print("  🔄 Trying JavaScript method...")
            
            js_drag_to_canvas = f"""
            function dragToCanvas(element, canvasX, canvasY) {{
                // Create drag events
                var dragStart = new DragEvent('dragstart', {{
                    bubbles: true,
                    cancelable: true,
                    dataTransfer: new DataTransfer()
                }});
                
                var drop = new DragEvent('drop', {{
                    bubbles: true,
                    cancelable: true,
                    clientX: canvasX,
                    clientY: canvasY,
                    dataTransfer: new DataTransfer()
                }});
                
                // Dispatch events
                element.dispatchEvent(dragStart);
                document.elementFromPoint(canvasX, canvasY).dispatchEvent(drop);
                
                return true;
            }}
            
            // Drag both elements to canvas
            dragToCanvas(arguments[0], {canvas_center_x}, {canvas_center_y});
            dragToCanvas(arguments[1], {canvas_center_x + 30}, {canvas_center_y + 30});
            
            return true;
            """
            
            driver.execute_script(js_drag_to_canvas, elem1, elem2)
            time.sleep(3)
            
            # Check again for new elements
            newer_elements = driver.find_elements(By.CSS_SELECTOR, ".item")
            newer_texts = [e.text for e in newer_elements if e.text.strip()]
            
            newer_items = [text for text in newer_texts if text not in initial_texts]
            
            if newer_items:
                for item in newer_items:
                    print(f"  🎉 NEW ITEM (JS): {item}")
                return newer_items[0]
            else:
                print("  ❌ No new items created")
                return None
            
    except Exception as e:
        print(f"  ❌ Combination failed: {e}")
        return None

def main():
    """Main automation function."""
    print("🚀 FIXED Infinite Craft Automation")
    print("=" * 40)
    
    driver = setup_driver()
    if not driver:
        return
    
    try:
        # Navigate to the game
        print("🌐 Loading Infinite Craft...")
        driver.get(GAME_URL)
        time.sleep(5)
        
        # Get elements and canvas
        elements, canvas = get_elements_and_canvas(driver)
        if len(elements) < 2 or not canvas:
            print("❌ Need at least 2 elements and canvas")
            return
        
        discovered_items = set()
        combinations_tried = set()
        
        print("\n🎮 Starting automation...")
        
        # Try basic combinations
        test_combinations = [
            (0, 1),  # Water + Fire
            (0, 2),  # Water + Wind  
            (0, 3),  # Water + Earth
            (1, 2),  # Fire + Wind
            (1, 3),  # Fire + Earth
            (2, 3),  # Wind + Earth
        ]
        
        for idx1, idx2 in test_combinations:
            if idx1 < len(elements) and idx2 < len(elements):
                elem1, elem2 = elements[idx1], elements[idx2]
                
                combo_key = tuple(sorted([elem1.text, elem2.text]))
                if combo_key in combinations_tried:
                    continue
                    
                combinations_tried.add(combo_key)
                
                # Attempt combination
                new_item = drag_elements_to_canvas(driver, elem1, elem2, canvas)
                
                if new_item:
                    discovered_items.add(new_item)
                    print(f"✅ SUCCESS! New item: {new_item}")
                    
                    # Refresh elements list
                    time.sleep(2)
                    elements, canvas = get_elements_and_canvas(driver)
                
                # Delay between attempts
                time.sleep(3)
        
        print(f"\n🎉 Automation completed!")
        print(f"📊 Items discovered: {len(discovered_items)}")
        for item in discovered_items:
            print(f"  • {item}")
            
        # Keep browser open to see results
        print("\n⏸️  Keeping browser open for 30 seconds to see results...")
        time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n⏹️  Stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        print("\n🔄 Closing browser...")
        driver.quit()
        print("✅ Done!")

if __name__ == "__main__":
    main()
