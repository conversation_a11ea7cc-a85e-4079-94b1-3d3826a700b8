{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "Python: Infinite Craft <PERSON>ript", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/infinite_craft_automation_script.py", "console": "integratedTerminal", "python": "${workspaceFolder}/.venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}